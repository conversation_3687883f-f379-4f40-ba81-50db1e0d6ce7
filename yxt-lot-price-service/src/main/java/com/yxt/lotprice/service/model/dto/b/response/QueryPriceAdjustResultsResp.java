package com.yxt.lotprice.service.model.dto.b.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 查询调价计算结果响应参数
 * 
 * <AUTHOR>
 * @since 2025-05-21
 */
@Data
@ApiModel("查询调价计算结果响应参数")
public class QueryPriceAdjustResultsResp implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("商品编码")
    private String erpCode;

    @ApiModelProperty("门店ID")
    private String storeId;

    @ApiModelProperty("门店编码")
    private String storeCode;

    @ApiModelProperty("公司编码")
    private String companyCode;

    @ApiModelProperty("价格类型")
    private String priceType;

    @ApiModelProperty("当前价格")
    private BigDecimal price;

    @ApiModelProperty("调价单编码")
    private String formNo;

    @ApiModelProperty("调价单明细编码")
    private String formItemNo;

    @ApiModelProperty("机构类型")
    private String orgType;

    @ApiModelProperty("价格生效开始时间")
    private LocalDate startTime;

    @ApiModelProperty("价格生效结束时间")
    private LocalDate endTime;

    @ApiModelProperty("下一次价格")
    private BigDecimal nextPrice;

    @ApiModelProperty("下一次价格生效开始时间")
    private LocalDate nextStartTime;

    @ApiModelProperty("下一次价格生效结束时间")
    private LocalDate nextEndTime;

    @ApiModelProperty("计算出的价格代")
    private String priceResultSegment;

    @ApiModelProperty("推送POS状态")
    private String pushPosStatus;

    @ApiModelProperty("创建时间")
    private LocalDateTime createdTime;

    @ApiModelProperty("更新时间")
    private LocalDateTime updatedTime;
}
